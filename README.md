# 智能压铸机健康监测系统

这是一个基于PyQt5开发的智能压铸机健康监测系统界面，提供了完整的数据分析、特征提取、健康诊断、实时监控和历史查询功能。

## 功能特性

### 1. 数据分析页面
- 原始数据展示表格
- 数据预处理（滤波及标准化）表格
- 原数据可视化图表
- 清洗后可视化图表
- 支持数据导入和预处理

### 2. 特征提取页面
- 特征数据展示表格
- 关键特征提取功能
- 特征趋势散点图
- 多种特征参数分析

### 3. 健康诊断页面
- 健康指数构造算法选择
- 健康指数结果展示图表
- RUL预测算法选择
- 3D RUL预测结果显示

### 4. 实时监控页面
- 特征参数趋势图（6个特征图表）
- 关键部件健康状态指示器
- 实时健康状态监控

### 5. 历史查询页面
- 时间范围选择
- 数据类型筛选
- 历史数据展示表格
- 报告导出功能（PDF/Excel/Word）

## 安装和运行

### 环境要求
- Python 3.7+
- PyQt5
- NumPy
- Matplotlib

### 安装步骤

1. 克隆或下载项目文件
2. 安装依赖包：
```bash
pip install -r requirements.txt
```

3. 运行程序：
```bash
python main.py
```

## 界面说明

### 头部区域
- 左侧：机器图标和"智能压铸机"标题
- 右侧："健康监测系统"副标题
- 深蓝色渐变背景

### 导航栏
- 五个主要功能模块按钮
- 右侧语言选择选项
- 浅蓝色渐变背景

### 内容区域
- 根据选择的导航按钮显示不同功能页面
- 统一的白色背景和现代化设计

## 数据说明

### 示例数据
系统包含以下示例数据：
- 压力、振动、应力、温度等传感器数据
- 特征提取参数（平均振幅、标准差、均方根振幅等）
- 健康指数和RUL预测数据
- 历史健康状态记录

### 数据可视化
- 使用Matplotlib绘制各种图表
- 支持2D和3D图表显示
- 实时数据更新和交互

## 技术特点

- **现代化UI设计**：使用PyQt5实现专业的工业监控界面
- **模块化架构**：各功能模块独立，便于维护和扩展
- **数据可视化**：丰富的图表展示，支持多种数据格式
- **响应式布局**：适配不同屏幕尺寸
- **中文界面**：完全中文化的用户界面

## 扩展功能

系统设计为可扩展架构，可以轻松添加：
- 新的数据分析算法
- 更多的可视化图表类型
- 数据库连接和数据持久化
- 网络通信和远程监控
- 报警和通知功能

## 注意事项

- 当前版本为演示版本，使用模拟数据
- 实际部署时需要连接真实的传感器数据源
- 建议在Windows环境下运行以获得最佳显示效果
- 图表显示可能需要根据实际数据格式进行调整 