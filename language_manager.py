class LanguageManager:
    """语言管理器，支持中英文切换"""
    
    def __init__(self):
        self.current_language = 'zh_CN'  # 默认中文
        self.languages = {
            'zh_CN': {
                # 头部
                'title': '智能压铸机',
                'subtitle': '健康监测系统',
                
                # 导航栏
                'nav_data_analysis': '数据分析',
                'nav_feature_extraction': '特征提取',
                'nav_health_diagnosis': '健康诊断',
                'nav_realtime_monitoring': '实时监控',
                'nav_history_query': '历史查询',
                'nav_machine_structure': '机器结构图',
                'language_selector': '简体中文 / 繁體中文 / English',
                
                # 数据分析页面
                'open_data_template': '打开数据模板',
                'import_data': '导入数据',
                'data_preprocessing': '数据预处理',
                'raw_data_visualization': '原数据可视化',
                'cleaned_visualization': '清洗后可视化',
                'raw_data_display': '原始数据展示',
                'data_preprocessing_display': '数据预处理 (滤波及标准化)',
                'measurement_time': '测量时间',
                'pressure': '压力',
                'vibration': '振动',
                'stress': '应力',
                'temperature': '温度',
                'raw_data_pressure_chart': '原数据压力图',
                'cleaned_pressure_chart': '清洗后压力图',
                'time': '时间',
                
                # 文件导入相关
                'select_data_file': '选择数据文件',
                'data_files': '数据文件 (*.csv *.xlsx *.xls)',
                'csv_files': 'CSV文件 (*.csv)',
                'excel_files': 'Excel文件 (*.xlsx *.xls)',
                'all_files': '所有文件 (*)',
                'error': '错误',
                'success': '成功',
                'unsupported_format': '不支持的文件格式',
                'insufficient_columns': '数据文件至少需要5列数据',
                'import_success': '成功导入数据文件：',
                'import_error': '导入文件时发生错误：',
                
                # 特征提取页面
                'key_feature_extraction': '关键特征提取',
                'feature_selection': '特征选择',
                'feature_data_display': '特征数据展示',
                'feature_trend_chart': '特征趋势图',
                'average_amplitude': '平均振幅',
                'standard_deviation': '标准差',
                'rms_amplitude': '均方根振幅',
                'rms_absolute_amplitude': '均方根绝对振幅',
                'kurtosis_coefficient': '峰度系数',
                'skewness_coefficient': '偏度系数',
                
                # 健康诊断页面
                'health_index_construction': '健康指数构造',
                'health_index_algorithm_selection': '健康指数构造算法选择:',
                'dropdown_select_algorithm': '下拉选择相应算法',
                'health_index_result_display': '健康指数结果展示',
                'health_index': '健康指数',
                'failure_threshold': '失效阈值',
                'rul_prediction': 'RUL预测',
                'rul_algorithm_selection': 'RUL预测算法选择:',
                'rul_prediction_result_display': 'RUL预测结果显示',
                'remaining_life_true_value': '剩余寿命真实值',
                'remaining_life_predicted_value': '剩余寿命预测值',
                'true_rul': '剩余寿命真实值',
                'predicted_rul': '剩余寿命预测值',
                'rul': 'RUL',
                
                # 实时监控页面
                'feature_parameter_trend_chart': '特征参数趋势图',
                'key_component_health_status': '关键部件健康状态',
                'normal': '正常',
                'abnormal': '异常',
                'fault': '故障',
                'normal_status': '正常',
                'abnormal_status': '异常',
                'fault_status': '故障',
                'die_casting_sleeve': '压铸套筒',
                'tie_bar': '哥林柱',
                'fixed_platen': '固定模板',
                'moving_platen': '移动模板',
                
                # 历史查询页面
                'time_range_label': '时间范围:',
                'time_range_default': '2025.05.01-2025.05.16',
                'data_type_label': '数据类型:',
                'data_type_default': '关键部件健康状态',
                'key_component_health_status_data': '关键部件健康状态',
                'historical_data_display': '历史数据展示',
                'remarks': '备注',
                'tie_bar_sensor_error': '哥林柱传感器数据异常',
                'tie_bar_break': '哥林柱断裂',
                'report_export_type_label': '报告导出类型:',
                'export_button': '导出',
                
                # 算法选项
                'algorithm_1': '算法1',
                'algorithm_2': '算法2',
                'algorithm_3': '算法3',
                
                # 导出格式
                'pdf_export': 'PDF',
                'excel_export': 'Excel',
                'word_export': 'Word',
                'pdf': 'PDF',
                'excel': 'Excel',
                'word': 'Word',
                
                # 机器结构图页面
                'machine_structure': '机器结构图',
                'tie_rods': '哥林柱*4',
                'moving_platen': '移动模板',
                'fixed_platen': '固定模板',
                'die_casting_sleeve': '压铸套筒',
                'control_panel': '控制面板',
                'image_load_failed': '图片加载失败',
                'image_load_error': '图片加载错误',
            },
            'en_US': {
                # Header
                'title': 'Intelligent Die Casting Machine',
                'subtitle': 'Health Monitoring System',
                
                # Navigation
                'nav_data_analysis': 'Data Analysis',
                'nav_feature_extraction': 'Feature Extraction',
                'nav_health_diagnosis': 'Health Diagnosis',
                'nav_realtime_monitoring': 'Real-time Monitoring',
                'nav_history_query': 'History Query',
                'nav_machine_structure': 'Machine Structure',
                'language_selector': '简体中文 / 繁體中文 / English',
                
                # Data Analysis Page
                'open_data_template': 'Open Data Template',
                'import_data': 'Import Data',
                'data_preprocessing': 'Data Preprocessing',
                'raw_data_visualization': 'Raw Data Visualization',
                'cleaned_visualization': 'Cleaned Visualization',
                'raw_data_display': 'Raw Data Display',
                'data_preprocessing_display': 'Data Preprocessing (Filtering & Standardization)',
                'measurement_time': 'Measurement Time',
                'pressure': 'Pressure',
                'vibration': 'Vibration',
                'stress': 'Stress',
                'temperature': 'Temperature',
                'raw_data_pressure_chart': 'Raw Data Pressure Chart',
                'cleaned_pressure_chart': 'Cleaned Pressure Chart',
                'time': 'Time',
                
                # File Import Related
                'select_data_file': 'Select Data File',
                'data_files': 'Data Files (*.csv *.xlsx *.xls)',
                'csv_files': 'CSV Files (*.csv)',
                'excel_files': 'Excel Files (*.xlsx *.xls)',
                'all_files': 'All Files (*)',
                'error': 'Error',
                'success': 'Success',
                'unsupported_format': 'Unsupported File Format',
                'insufficient_columns': 'Data file must have at least 5 columns',
                'import_success': 'Data file successfully imported: ',
                'import_error': 'Error importing file: ',
                
                # Feature Extraction Page
                'key_feature_extraction': 'Key Feature Extraction',
                'feature_selection': 'Feature Selection',
                'feature_data_display': 'Feature Data Display',
                'feature_trend_chart': 'Feature Trend Chart',
                'average_amplitude': 'Average Amplitude',
                'standard_deviation': 'Standard Deviation',
                'rms_amplitude': 'RMS Amplitude',
                'rms_absolute_amplitude': 'RMS Absolute Amplitude',
                'kurtosis_coefficient': 'Kurtosis Coefficient',
                'skewness_coefficient': 'Skewness Coefficient',
                
                # Health Diagnosis Page
                'health_index_construction': 'Health Index Construction',
                'health_index_algorithm_selection': 'Health Index Algorithm Selection:',
                'dropdown_select_algorithm': 'Dropdown to select algorithm',
                'health_index_result_display': 'Health Index Result Display',
                'health_index': 'Health Index',
                'failure_threshold': 'Failure Threshold',
                'rul_prediction': 'RUL Prediction',
                'rul_algorithm_selection': 'RUL Prediction Algorithm Selection:',
                'rul_prediction_result_display': 'RUL Prediction Result Display',
                'remaining_life_true_value': 'Remaining Life True Value',
                'remaining_life_predicted_value': 'Remaining Life Predicted Value',
                'true_rul': 'True RUL',
                'predicted_rul': 'Predicted RUL',
                'rul': 'RUL',
                
                # Real-time Monitoring Page
                'feature_parameter_trend_chart': 'Feature Parameter Trend Chart',
                'key_component_health_status': 'Key Component Health Status',
                'normal': 'Normal',
                'abnormal': 'Abnormal',
                'fault': 'Fault',
                'normal_status': 'Normal',
                'abnormal_status': 'Abnormal',
                'fault_status': 'Fault',
                'die_casting_sleeve': 'Die Casting Sleeve',
                'tie_bar': 'Tie Bar',
                'fixed_platen': 'Fixed Platen',
                'moving_platen': 'Moving Platen',
                
                # History Query Page
                'time_range_label': 'Time Range:',
                'time_range_default': '2025.05.01-2025.05.16',
                'data_type_label': 'Data Type:',
                'data_type_default': 'Key Component Health Status',
                'key_component_health_status_data': 'Key Component Health Status',
                'historical_data_display': 'Historical Data Display',
                'remarks': 'Remarks',
                'tie_bar_sensor_error': 'Tie bar sensor data abnormal',
                'tie_bar_break': 'Tie bar fractured',
                'report_export_type_label': 'Report Export Type:',
                'export_button': 'Export',
                
                # Algorithm options
                'algorithm_1': 'Algorithm 1',
                'algorithm_2': 'Algorithm 2',
                'algorithm_3': 'Algorithm 3',
                
                # Export formats
                'pdf_export': 'PDF',
                'excel_export': 'Excel',
                'word_export': 'Word',
                'pdf': 'PDF',
                'excel': 'Excel',
                'word': 'Word',
                
                # Machine Structure Page
                'machine_structure': 'Machine Structure',
                'tie_rods': 'Tie Rods*4',
                'moving_platen': 'Moving Platen',
                'fixed_platen': 'Fixed Platen',
                'die_casting_sleeve': 'Die Casting Sleeve',
                'control_panel': 'Control Panel',
                'image_load_failed': 'Image Load Failed',
                'image_load_error': 'Image Load Error',
            }
        }
    
    def get_text(self, key):
        """获取指定键的文本"""
        return self.languages[self.current_language].get(key, key)
    
    def switch_language(self, language):
        """切换语言"""
        if language in self.languages:
            self.current_language = language
            return True
        return False
    
    def get_current_language(self):
        """获取当前语言"""
        return self.current_language
    
    def get_available_languages(self):
        """获取可用语言列表"""
        return list(self.languages.keys()) 