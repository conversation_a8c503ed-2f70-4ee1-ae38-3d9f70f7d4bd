import sys
import numpy as np
import pandas as pd
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QLabel, QPushButton, QTableWidget, 
                             QTableWidgetItem, QTabWidget, QFrame, QGridLayout,
                             QComboBox, QLineEdit, QGroupBox, QScrollArea,
                             QFileDialog, QMessageBox)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QPalette, QColor, QPixmap, QPainter, QPen
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import random
from language_manager import LanguageManager

# 设置matplotlib中文字体
def setup_chinese_font():
    """设置中文字体"""
    import platform
    system = platform.system()
    
    if system == 'Windows':
        # Windows系统字体
        font_list = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi']
    elif system == 'Darwin':
        # macOS系统字体
        font_list = ['PingFang SC', 'Hiragino Sans GB', 'STHeiti']
    else:
        # Linux系统字体
        font_list = ['WenQuanYi Micro Hei', 'DejaVu Sans', 'Liberation Sans']
    
    # 尝试设置字体
    for font in font_list:
        try:
            plt.rcParams['font.sans-serif'] = [font] + plt.rcParams['font.sans-serif']
            break
        except:
            continue
    
    plt.rcParams['axes.unicode_minus'] = False

# 初始化字体设置
setup_chinese_font()

class HealthMonitoringSystem(QMainWindow):
    def __init__(self):
        super().__init__()
        self.lang_manager = LanguageManager()
        self.initUI()
        
    def initUI(self):
        """初始化用户界面"""
        self.setWindowTitle('智能压铸机健康监测系统')
        self.setGeometry(100, 100, 1400, 900)
        
        # 设置样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QLabel {
                color: #333333;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(0)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建头部
        self.create_header(main_layout)
        
        # 创建导航栏
        self.create_navigation(main_layout)
        
        # 创建内容区域
        self.create_content_area(main_layout)
        
    def create_header(self, parent_layout):
        """创建头部区域"""
        header = QFrame()
        header.setFixedHeight(80)
        header.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 #1e3a8a, stop:1 #3b82f6);
                border: none;
            }
        """)
        
        header_layout = QHBoxLayout(header)
        header_layout.setContentsMargins(20, 10, 20, 10)
        
        # 左侧图标和标题
        left_widget = QWidget()
        left_layout = QHBoxLayout(left_widget)
        
        # 机器图标（使用文字模拟）
        icon_label = QLabel("⚙️")
        icon_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24px;
                font-weight: bold;
            }
        """)
        
        # 主标题
        self.title_label = QLabel(self.lang_manager.get_text('title'))
        self.title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24px;
                font-weight: bold;
            }
        """)
        
        left_layout.addWidget(icon_label)
        left_layout.addWidget(self.title_label)
        left_layout.addStretch()
        
        # 右侧副标题
        self.subtitle_label = QLabel(self.lang_manager.get_text('subtitle'))
        self.subtitle_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
            }
        """)
        
        header_layout.addWidget(left_widget)
        header_layout.addStretch()
        header_layout.addWidget(self.subtitle_label)
        
        parent_layout.addWidget(header)
        
    def create_navigation(self, parent_layout):
        """创建导航栏"""
        nav_frame = QFrame()
        nav_frame.setFixedHeight(60)
        nav_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 #60a5fa, stop:1 #93c5fd);
                border: none;
            }
        """)
        
        nav_layout = QHBoxLayout(nav_frame)
        nav_layout.setContentsMargins(20, 10, 20, 10)
        
        # 导航按钮
        nav_texts = [
            'nav_data_analysis', 'nav_feature_extraction', 'nav_health_diagnosis', 
            'nav_realtime_monitoring', 'nav_history_query', 'nav_machine_structure'
        ]
        
        self.nav_buttons = []
        for i, text_key in enumerate(nav_texts):
            btn = QPushButton(self.lang_manager.get_text(text_key))
            btn.setFixedSize(120, 40)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: transparent;
                    color: white;
                    border: 2px solid transparent;
                    border-radius: 5px;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: rgba(255, 255, 255, 0.2);
                }
                QPushButton:pressed {
                    background-color: rgba(255, 255, 255, 0.3);
                }
            """)
            btn.clicked.connect(lambda checked, index=i: self.on_nav_button_clicked(index))
            self.nav_buttons.append(btn)
            nav_layout.addWidget(btn)
            
        nav_layout.addStretch()
        
        # 语言选择下拉框
        self.lang_combo = QComboBox()
        self.lang_combo.addItems(['简体中文', 'English'])
        self.lang_combo.setCurrentText('简体中文')
        self.lang_combo.setFixedSize(150, 30)
        self.lang_combo.setStyleSheet("""
            QComboBox {
                background-color: rgba(255, 255, 255, 0.2);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 3px;
                padding: 5px;
                font-size: 12px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid white;
            }
        """)
        self.lang_combo.currentTextChanged.connect(self.on_language_changed)
        nav_layout.addWidget(self.lang_combo)
        
        parent_layout.addWidget(nav_frame)
        
    def create_content_area(self, parent_layout):
        """创建内容区域"""
        self.content_stack = QWidget()
        content_layout = QVBoxLayout(self.content_stack)
        content_layout.setContentsMargins(20, 20, 20, 20)
        
        # 创建不同的页面
        self.create_data_analysis_page(content_layout)
        self.create_feature_extraction_page(content_layout)
        self.create_health_diagnosis_page(content_layout)
        self.create_realtime_monitoring_page(content_layout)
        self.create_history_query_page(content_layout)
        self.create_machine_structure_page(content_layout)
        
        # 默认显示数据分析页面
        self.show_page(0)
        
        parent_layout.addWidget(self.content_stack)
        
    def create_data_analysis_page(self, parent_layout):
        """创建数据分析页面"""
        self.data_analysis_widget = QWidget()
        layout = QVBoxLayout(self.data_analysis_widget)
        
        # 控制按钮区域
        control_layout = QHBoxLayout()
        control_layout.addStretch()
        
        button_texts = ['open_data_template', 'import_data', 'data_preprocessing', 'raw_data_visualization', 'cleaned_visualization']
        self.data_analysis_buttons = []
        for i, text_key in enumerate(button_texts):
            btn = QPushButton(self.lang_manager.get_text(text_key))
            btn.setFixedSize(120, 35)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #6b7280;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #4b5563;
                }
            """)
            # 为导入数据按钮添加点击事件
            if text_key == 'import_data':
                btn.clicked.connect(self.import_data_file)
            self.data_analysis_buttons.append(btn)
            control_layout.addWidget(btn)
            
        layout.addLayout(control_layout)
        
        # 数据展示区域
        data_layout = QHBoxLayout()
        
        # 左侧数据表格
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        # 原始数据表格
        raw_data_group = QGroupBox(self.lang_manager.get_text('raw_data_display'))
        raw_data_layout = QVBoxLayout(raw_data_group)
        
        self.raw_data_table = QTableWidget(8, 5)
        headers = [
            self.lang_manager.get_text('measurement_time'),
            self.lang_manager.get_text('pressure'),
            self.lang_manager.get_text('vibration'),
            self.lang_manager.get_text('stress'),
            self.lang_manager.get_text('temperature')
        ]
        self.raw_data_table.setHorizontalHeaderLabels(headers)
        
        # 填充示例数据
        raw_data = [
            [0, 10.154671, 10.898891, 0.112899, 44.824441],
            [0.000195, 8.932424, 10.075667, 0.113272, 44.824441],
            [0.000293, 8.032415, 9.956389, 0.114223, 44.824441],
            [0.000391, 7.339201, 9.410238, 0.113617, 44.824441],
            [0.000488, 6.822213, 9.384844, 0.111783, 44.824441],
            [0.000586, 6.515108, 7.876841, 0.112843, 44.824441],
            [0.000684, 6.446784, 5.13284, 0.11328, 44.824441],
            [0.000781, 6.650846, 3.368151, 0.114448, 44.824441]
        ]
        
        for i, row in enumerate(raw_data):
            for j, value in enumerate(row):
                item = QTableWidgetItem(str(value))
                self.raw_data_table.setItem(i, j, item)
                if j == 1:  # 压力列高亮
                    item.setBackground(QColor(255, 255, 0, 100))
                    
        self.raw_data_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                gridline-color: #d1d5db;
            }
            QHeaderView::section {
                background-color: #f3f4f6;
                padding: 5px;
                border: 1px solid #d1d5db;
                font-weight: bold;
            }
        """)
        
        raw_data_layout.addWidget(self.raw_data_table)
        left_layout.addWidget(raw_data_group)
        
        # 预处理数据表格
        processed_data_group = QGroupBox(self.lang_manager.get_text('data_preprocessing_display'))
        processed_data_layout = QVBoxLayout(processed_data_group)
        
        self.processed_data_table = QTableWidget(8, 5)
        headers = [
            self.lang_manager.get_text('measurement_time'),
            self.lang_manager.get_text('pressure'),
            self.lang_manager.get_text('vibration'),
            self.lang_manager.get_text('stress'),
            self.lang_manager.get_text('temperature')
        ]
        self.processed_data_table.setHorizontalHeaderLabels(headers)
        
        # 填充预处理后的示例数据
        processed_data = [
            [0, -1.408667, 1.459792, 0.305772, -1.312748],
            [0.000195, -1.41880277, 1.2549571, 0.306781, -1.312748],
            [0.000293, -1.4262663, 1.2252784, 0.309353, -1.312748],
            [0.000391, -1.43201494, 1.0893849, 0.307714, -1.312748],
            [0.000488, -1.43630218, 1.0830664, 0.302753, -1.312748],
            [0.000586, -1.43884892, 0.7078447, 0.30562, -1.312748],
            [0.000684, -1.43941551, 0.0250816, 0.306802, -1.312748],
            [0.000781, -1.43772328, -0.414009, 0.309962, -1.312748]
        ]
        
        for i, row in enumerate(processed_data):
            for j, value in enumerate(row):
                item = QTableWidgetItem(str(value))
                self.processed_data_table.setItem(i, j, item)
                if j == 1:  # 压力列高亮
                    item.setBackground(QColor(255, 255, 0, 100))
                    
        self.processed_data_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                gridline-color: #d1d5db;
            }
            QHeaderView::section {
                background-color: #f3f4f6;
                padding: 5px;
                border: 1px solid #d1d5db;
                font-weight: bold;
            }
        """)
        
        processed_data_layout.addWidget(self.processed_data_table)
        left_layout.addWidget(processed_data_group)
        
        data_layout.addWidget(left_panel, 1)
        
        # 右侧图表
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # 原数据可视化
        raw_plot_group = QGroupBox(self.lang_manager.get_text('raw_data_visualization'))
        raw_plot_layout = QVBoxLayout(raw_plot_group)
        
        self.raw_figure = Figure(figsize=(6, 3))
        self.raw_canvas = FigureCanvas(self.raw_figure)
        self.create_pressure_plot(self.raw_figure, self.lang_manager.get_text('raw_data_pressure_chart'), 0, 200)
        raw_plot_layout.addWidget(self.raw_canvas)
        right_layout.addWidget(raw_plot_group)
        
        # 清洗后可视化
        cleaned_plot_group = QGroupBox(self.lang_manager.get_text('cleaned_visualization'))
        cleaned_plot_layout = QVBoxLayout(cleaned_plot_group)
        
        self.cleaned_figure = Figure(figsize=(6, 3))
        self.cleaned_canvas = FigureCanvas(self.cleaned_figure)
        self.create_pressure_plot(self.cleaned_figure, self.lang_manager.get_text('cleaned_pressure_chart'), -1.5, 1.0)
        cleaned_plot_layout.addWidget(self.cleaned_canvas)
        right_layout.addWidget(cleaned_plot_group)
        
        data_layout.addWidget(right_panel, 1)
        layout.addLayout(data_layout)
        
        parent_layout.addWidget(self.data_analysis_widget)
        self.data_analysis_widget.hide()
        
    def create_feature_extraction_page(self, parent_layout):
        """创建特征提取页面"""
        self.feature_extraction_widget = QWidget()
        layout = QVBoxLayout(self.feature_extraction_widget)
        
        # 控制按钮区域
        control_layout = QHBoxLayout()
        control_layout.addStretch()
        
        button_texts = [
            self.lang_manager.get_text('open_data_template'),
            self.lang_manager.get_text('import_data'),
            self.lang_manager.get_text('key_feature_extraction'),
            self.lang_manager.get_text('feature_selection')
        ]
        for i, text in enumerate(button_texts):
            btn = QPushButton(text)
            btn.setFixedSize(120, 35)
            if i == 2:  # 关键特征提取按钮高亮
                btn.setStyleSheet("""
                    QPushButton {
                        background-color: #3b82f6;
                        color: white;
                        border: none;
                        border-radius: 5px;
                        font-size: 12px;
                    }
                    QPushButton:hover {
                        background-color: #2563eb;
                    }
                """)
            else:
                btn.setStyleSheet("""
                    QPushButton {
                        background-color: #6b7280;
                        color: white;
                        border: none;
                        border-radius: 5px;
                        font-size: 12px;
                    }
                    QPushButton:hover {
                        background-color: #4b5563;
                    }
                """)
            control_layout.addWidget(btn)
            
        layout.addLayout(control_layout)
        
        # 数据展示区域
        data_layout = QHBoxLayout()
        
        # 左侧数据表格
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        feature_data_group = QGroupBox(self.lang_manager.get_text('feature_data_display'))
        feature_data_layout = QVBoxLayout(feature_data_group)
        
        self.feature_data_table = QTableWidget(20, 6)
        self.feature_data_table.setHorizontalHeaderLabels([
            self.lang_manager.get_text('measurement_time'),
            self.lang_manager.get_text('average_amplitude'),
            self.lang_manager.get_text('standard_deviation'),
            self.lang_manager.get_text('rms_amplitude'),
            self.lang_manager.get_text('rms_absolute_amplitude'),
            self.lang_manager.get_text('kurtosis_coefficient')
        ])
        
        # 填充特征数据
        for i in range(20):
            for j in range(6):
                if j == 0:
                    value = i + 1
                else:
                    value = round(random.uniform(0.1, 10.0), 6)
                item = QTableWidgetItem(str(value))
                self.feature_data_table.setItem(i, j, item)
                if j == 3:  # 均方根振幅列高亮
                    item.setBackground(QColor(255, 255, 0, 100))
                    
        self.feature_data_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                gridline-color: #d1d5db;
            }
            QHeaderView::section {
                background-color: #f3f4f6;
                padding: 5px;
                border: 1px solid #d1d5db;
                font-weight: bold;
            }
        """)
        
        feature_data_layout.addWidget(self.feature_data_table)
        left_layout.addWidget(feature_data_group)
        
        data_layout.addWidget(left_panel, 1)
        
        # 右侧散点图
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        plot_group = QGroupBox(self.lang_manager.get_text('feature_trend_chart'))
        plot_layout = QVBoxLayout(plot_group)
        
        self.feature_figure = Figure(figsize=(6, 6))
        self.feature_canvas = FigureCanvas(self.feature_figure)
        self.create_feature_scatter_plot()
        plot_layout.addWidget(self.feature_canvas)
        right_layout.addWidget(plot_group)
        
        data_layout.addWidget(right_panel, 1)
        layout.addLayout(data_layout)
        
        parent_layout.addWidget(self.feature_extraction_widget)
        self.feature_extraction_widget.hide()
        
    def create_health_diagnosis_page(self, parent_layout):
        """创建健康诊断页面"""
        self.health_diagnosis_widget = QWidget()
        layout = QVBoxLayout(self.health_diagnosis_widget)
        
        # 创建左右分栏
        content_layout = QHBoxLayout()
        
        # 左侧健康指数构造
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        health_index_group = QGroupBox(self.lang_manager.get_text('health_index_construction'))
        health_index_layout = QVBoxLayout(health_index_group)
        
        # 算法选择
        algo_layout = QHBoxLayout()
        algo_label = QLabel(self.lang_manager.get_text('health_index_algorithm_selection'))
        self.algo_combo = QComboBox()
        self.algo_combo.addItems([
            self.lang_manager.get_text('dropdown_select_algorithm'),
            self.lang_manager.get_text('algorithm_1'),
            self.lang_manager.get_text('algorithm_2'),
            self.lang_manager.get_text('algorithm_3')
        ])
        self.algo_combo.setStyleSheet("""
            QComboBox {
                padding: 5px;
                border: 1px solid #d1d5db;
                border-radius: 3px;
                background-color: white;
            }
        """)
        algo_layout.addWidget(algo_label)
        algo_layout.addWidget(self.algo_combo)
        algo_layout.addStretch()
        health_index_layout.addLayout(algo_layout)
        
        # 健康指数图表
        self.health_figure = Figure(figsize=(6, 4))
        self.health_canvas = FigureCanvas(self.health_figure)
        self.create_health_index_plot()
        health_index_layout.addWidget(self.health_canvas)
        
        left_layout.addWidget(health_index_group)
        content_layout.addWidget(left_panel, 1)
        
        # 右侧RUL预测
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        rul_group = QGroupBox(self.lang_manager.get_text('rul_prediction'))
        rul_layout = QVBoxLayout(rul_group)
        
        # 算法选择
        rul_algo_layout = QHBoxLayout()
        rul_algo_label = QLabel(self.lang_manager.get_text('rul_algorithm_selection'))
        self.rul_algo_combo = QComboBox()
        self.rul_algo_combo.addItems([
            self.lang_manager.get_text('dropdown_select_algorithm'),
            self.lang_manager.get_text('algorithm_1'),
            self.lang_manager.get_text('algorithm_2'),
            self.lang_manager.get_text('algorithm_3')
        ])
        self.rul_algo_combo.setStyleSheet("""
            QComboBox {
                padding: 5px;
                border: 1px solid #d1d5db;
                border-radius: 3px;
                background-color: white;
            }
        """)
        rul_algo_layout.addWidget(rul_algo_label)
        rul_algo_layout.addWidget(self.rul_algo_combo)
        rul_algo_layout.addStretch()
        rul_layout.addLayout(rul_algo_layout)
        
        # RUL预测图表
        self.rul_figure = Figure(figsize=(6, 4))
        self.rul_canvas = FigureCanvas(self.rul_figure)
        self.create_rul_prediction_plot()
        rul_layout.addWidget(self.rul_canvas)
        
        right_layout.addWidget(rul_group)
        content_layout.addWidget(right_panel, 1)
        
        layout.addLayout(content_layout)
        
        parent_layout.addWidget(self.health_diagnosis_widget)
        self.health_diagnosis_widget.hide()
        
    def create_realtime_monitoring_page(self, parent_layout):
        """创建实时监控页面"""
        self.realtime_monitoring_widget = QWidget()
        layout = QVBoxLayout(self.realtime_monitoring_widget)
        
        # 创建左右分栏
        content_layout = QHBoxLayout()
        
        # 左侧特征参数趋势图
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        trend_group = QGroupBox(self.lang_manager.get_text('feature_parameter_trend_chart'))
        trend_layout = QVBoxLayout(trend_group)
        
        # 创建6个特征图表的网格
        self.trend_figure = Figure(figsize=(8, 6))
        self.trend_canvas = FigureCanvas(self.trend_figure)
        self.create_trend_plots()
        trend_layout.addWidget(self.trend_canvas)
        
        left_layout.addWidget(trend_group)
        content_layout.addWidget(left_panel, 1)
        
        # 右侧关键部件健康状态
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        health_status_group = QGroupBox(self.lang_manager.get_text('key_component_health_status'))
        health_status_layout = QVBoxLayout(health_status_group)
        
        # 创建健康状态表格
        self.health_status_table = QTableWidget(4, 3)
        self.health_status_table.setHorizontalHeaderLabels([
            self.lang_manager.get_text('normal'),
            self.lang_manager.get_text('abnormal'),
            self.lang_manager.get_text('fault')
        ])
        self.health_status_table.setVerticalHeaderLabels([
            self.lang_manager.get_text('die_casting_sleeve'),
            self.lang_manager.get_text('tie_bar'),
            self.lang_manager.get_text('fixed_platen'),
            self.lang_manager.get_text('moving_platen')
        ])
        
        # 设置健康状态指示器
        for i in range(4):
            for j in range(3):
                item = QTableWidgetItem()
                item.setFlags(Qt.ItemIsEnabled)
                if j == 0:  # 正常状态（绿色）
                    item.setBackground(QColor(34, 197, 94))
                self.health_status_table.setItem(i, j, item)
                
        self.health_status_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                gridline-color: #d1d5db;
            }
            QHeaderView::section {
                background-color: #f3f4f6;
                padding: 10px;
                border: 1px solid #d1d5db;
                font-weight: bold;
            }
        """)
        
        health_status_layout.addWidget(self.health_status_table)
        right_layout.addWidget(health_status_group)
        content_layout.addWidget(right_panel, 1)
        
        layout.addLayout(content_layout)
        
        parent_layout.addWidget(self.realtime_monitoring_widget)
        self.realtime_monitoring_widget.hide()
        
    def create_history_query_page(self, parent_layout):
        """创建历史查询页面"""
        self.history_query_widget = QWidget()
        layout = QVBoxLayout(self.history_query_widget)
        
        # 顶部控制区域
        control_layout = QHBoxLayout()
        
        # 时间范围选择
        self.time_label = QLabel(self.lang_manager.get_text('time_range_label'))
        self.time_input = QLineEdit(self.lang_manager.get_text('time_range_default'))
        self.time_input.setFixedWidth(200)
        self.time_input.setStyleSheet("""
            QLineEdit {
                padding: 5px;
                border: 1px solid #d1d5db;
                border-radius: 3px;
                background-color: white;
            }
        """)
        
        # 数据类型选择
        self.data_type_label = QLabel(self.lang_manager.get_text('data_type_label'))
        self.data_type_input = QLineEdit(self.lang_manager.get_text('data_type_default'))
        self.data_type_input.setFixedWidth(200)
        self.data_type_input.setStyleSheet("""
            QLineEdit {
                padding: 5px;
                border: 1px solid #d1d5db;
                border-radius: 3px;
                background-color: white;
            }
        """)
        
        control_layout.addWidget(self.time_label)
        control_layout.addWidget(self.time_input)
        control_layout.addStretch()
        control_layout.addWidget(self.data_type_label)
        control_layout.addWidget(self.data_type_input)
        
        layout.addLayout(control_layout)
        
        # 历史数据表格
        history_group = QGroupBox(self.lang_manager.get_text('historical_data_display'))
        history_layout = QVBoxLayout(history_group)
        
        self.history_table = QTableWidget(18, 6)
        self.history_table.setHorizontalHeaderLabels([
            self.lang_manager.get_text('measurement_time'),
            self.lang_manager.get_text('die_casting_sleeve'),
            self.lang_manager.get_text('tie_bar'),
            self.lang_manager.get_text('fixed_platen'),
            self.lang_manager.get_text('moving_platen'),
            self.lang_manager.get_text('remarks')
        ])
        
        # 填充历史数据
        dates = []
        current_date = datetime(2025, 5, 1)
        for i in range(18):
            dates.append(current_date.strftime("%Y.%m.%d"))
            current_date += timedelta(days=1)
            
        for i in range(18):
            # 测量时间
            self.history_table.setItem(i, 0, QTableWidgetItem(dates[i]))
            
            # 各部件状态
            for j in range(1, 5):
                item = QTableWidgetItem(self.lang_manager.get_text('normal_status'))
                item.setBackground(QColor(34, 197, 94, 100))  # 绿色背景
                self.history_table.setItem(i, j, item)
                
            # 备注
            self.history_table.setItem(i, 5, QTableWidgetItem(""))
            
        # 设置异常数据
        # 2025.05.17 哥林柱异常
        self.history_table.setItem(16, 2, QTableWidgetItem(self.lang_manager.get_text('abnormal_status')))
        self.history_table.item(16, 2).setBackground(QColor(251, 191, 36, 100))  # 黄色背景
        self.history_table.setItem(16, 5, QTableWidgetItem(self.lang_manager.get_text('tie_bar_sensor_error')))
        
        # 2025.05.18 哥林柱故障
        self.history_table.setItem(17, 2, QTableWidgetItem(self.lang_manager.get_text('fault_status')))
        self.history_table.item(17, 2).setBackground(QColor(239, 68, 68, 100))  # 红色背景
        self.history_table.setItem(17, 5, QTableWidgetItem(self.lang_manager.get_text('tie_bar_break')))
        
        self.history_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                gridline-color: #d1d5db;
            }
            QHeaderView::section {
                background-color: #f3f4f6;
                padding: 10px;
                border: 1px solid #d1d5db;
                font-weight: bold;
            }
        """)
        
        history_layout.addWidget(self.history_table)
        layout.addWidget(history_group)
        
        # 底部导出区域
        export_layout = QHBoxLayout()
        export_layout.addStretch()
        
        self.export_type_label = QLabel(self.lang_manager.get_text('report_export_type_label'))
        self.export_type_combo = QComboBox()
        self.export_type_combo.addItems([self.lang_manager.get_text('pdf_export'), self.lang_manager.get_text('excel_export'), self.lang_manager.get_text('word_export')])
        self.export_type_combo.setCurrentText(self.lang_manager.get_text('pdf_export'))
        self.export_type_combo.setFixedWidth(100)
        self.export_type_combo.setStyleSheet("""
            QComboBox {
                padding: 5px;
                border: 1px solid #d1d5db;
                border-radius: 3px;
                background-color: white;
            }
        """)
        
        self.export_btn = QPushButton(self.lang_manager.get_text('export_button'))
        self.export_btn.setFixedSize(80, 35)
        self.export_btn.setStyleSheet("""
            QPushButton {
                background-color: #3b82f6;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2563eb;
            }
        """)
        
        export_layout.addWidget(self.export_type_label)
        export_layout.addWidget(self.export_type_combo)
        export_layout.addWidget(self.export_btn)
        
        layout.addLayout(export_layout)
        
        parent_layout.addWidget(self.history_query_widget)
        self.history_query_widget.hide()
        
    def create_machine_structure_page(self, parent_layout):
        """创建机器结构图页面"""
        self.machine_structure_widget = QWidget()
        layout = QVBoxLayout(self.machine_structure_widget)
        layout.setContentsMargins(0, 0, 0, 0)  # 完全移除边距
        
        # 创建图片标签
        self.machine_image_label = QLabel()
        self.machine_image_label.setAlignment(Qt.AlignCenter)
        self.machine_image_label.setStyleSheet("""
            QLabel {
                border: none;
                background-color: transparent;
            }
        """)
        
        # 加载图片
        self.load_machine_image()
        
        layout.addWidget(self.machine_image_label)
        
        parent_layout.addWidget(self.machine_structure_widget)
        self.machine_structure_widget.hide()
        
    def load_machine_image(self):
        """加载机器结构图"""
        try:
            pixmap = QPixmap("bg.png")
            if not pixmap.isNull():
                # 调整图片大小以适应窗口，占据更大空间
                scaled_pixmap = pixmap.scaled(1400, 900, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                self.machine_image_label.setPixmap(scaled_pixmap)
            else:
                # 如果图片加载失败，显示错误信息
                self.machine_image_label.setText(self.lang_manager.get_text('image_load_failed'))
                self.machine_image_label.setStyleSheet("""
                    QLabel {
                        border: 2px solid #e74c3c;
                        border-radius: 10px;
                        background-color: #fdf2f2;
                        color: #e74c3c;
                        font-size: 16px;
                        padding: 20px;
                    }
                """)
        except Exception as e:
            self.machine_image_label.setText(self.lang_manager.get_text('image_load_error') + f": {str(e)}")
            self.machine_image_label.setStyleSheet("""
                QLabel {
                    border: 2px solid #e74c3c;
                    border-radius: 10px;
                    background-color: #fdf2f2;
                    color: #e74c3c;
                    font-size: 16px;
                    padding: 20px;
                }
            """)
        
    def create_pressure_plot(self, figure, title, y_min, y_max):
        """创建压力图表"""
        ax = figure.add_subplot(111)
        
        # 生成示例数据
        x = np.linspace(1, 26, 1000)
        y = np.sin(x * 0.5) * 50 + 100 + np.random.normal(0, 10, 1000)
        
        # 添加平坦区域（模拟停机）
        flat_mask = (x >= 4) & (x <= 7)
        y[flat_mask] = np.random.normal(0, 2, np.sum(flat_mask))
        
        ax.plot(x, y, 'b-', linewidth=1)
        ax.set_xlabel(self.lang_manager.get_text('time'), fontsize=10)
        ax.set_ylabel(self.lang_manager.get_text('pressure'), fontsize=10)
        ax.set_title(title, fontsize=12)
        ax.set_ylim(y_min, y_max)
        ax.grid(True, alpha=0.3)
        
        # 设置字体
        for label in ax.get_xticklabels() + ax.get_yticklabels():
            label.set_fontsize(9)
        
        figure.tight_layout()
        
    def create_feature_scatter_plot(self):
        """创建特征散点图"""
        ax = self.feature_figure.add_subplot(111)
        
        # 生成示例数据
        x = np.arange(1, 27)
        y = np.random.normal(5, 1, 26) + np.linspace(0, 2, 26)
        
        ax.scatter(x, y, c='blue', s=50)
        ax.set_xlabel(self.lang_manager.get_text('time'), fontsize=10)
        ax.set_ylabel(self.lang_manager.get_text('rms_amplitude'), fontsize=10)
        ax.set_title(self.lang_manager.get_text('feature_trend_chart'), fontsize=12)
        ax.grid(True, alpha=0.3)
        
        # 设置字体
        for label in ax.get_xticklabels() + ax.get_yticklabels():
            label.set_fontsize(9)
        
        self.feature_figure.tight_layout()
        
    def create_health_index_plot(self):
        """创建健康指数图"""
        ax = self.health_figure.add_subplot(111)
        
        # 生成示例数据
        x = np.arange(1, 28)
        y = np.random.normal(0.6, 0.1, 27) + np.linspace(0, 0.3, 27)
        
        ax.scatter(x, y, c='blue', s=50)
        ax.axhline(y=1.2, color='red', linestyle='--', label=self.lang_manager.get_text('failure_threshold'))
        ax.set_xlabel(self.lang_manager.get_text('time'), fontsize=10)
        ax.set_ylabel(self.lang_manager.get_text('health_index'), fontsize=10)
        ax.set_title(self.lang_manager.get_text('health_index_result_display'), fontsize=12)
        ax.legend(fontsize=9)
        ax.grid(True, alpha=0.3)
        
        # 设置字体
        for label in ax.get_xticklabels() + ax.get_yticklabels():
            label.set_fontsize(9)
        
        self.health_figure.tight_layout()
        
    def create_rul_prediction_plot(self):
        """创建RUL预测图"""
        ax = self.rul_figure.add_subplot(111, projection='3d')
        
        # 生成示例数据
        time_points = np.arange(2, 7)
        rul_values = np.arange(0, 11)
        T, R = np.meshgrid(time_points, rul_values)
        
        # 创建PDF数据
        Z = np.zeros_like(T)
        for i, t in enumerate(time_points):
            mean_rul = 10 - t * 1.5
            Z[:, i] = np.exp(-0.5 * ((R[:, i] - mean_rul) / 2) ** 2)
            
        # 绘制3D表面
        surf = ax.plot_surface(T, R, Z, alpha=0.6, cmap='viridis')
        
        # 添加真实值和预测值
        true_rul = 10 - time_points * 1.2
        pred_rul = 10 - time_points * 1.5 + np.random.normal(0, 0.5, len(time_points))
        
        ax.scatter(time_points, true_rul, Z.max(axis=0), c='blue', s=50, label=self.lang_manager.get_text('true_rul'))
        ax.scatter(time_points, pred_rul, Z.max(axis=0), c='black', s=50, label=self.lang_manager.get_text('predicted_rul'))
        
        ax.set_xlabel(self.lang_manager.get_text('time'), fontsize=10)
        ax.set_ylabel(self.lang_manager.get_text('rul'), fontsize=10)
        ax.set_zlabel(self.lang_manager.get_text('pdf'), fontsize=10)
        ax.set_title(self.lang_manager.get_text('rul_prediction_result_display'), fontsize=12)
        ax.legend(fontsize=9)
        
        # 设置字体
        for label in ax.get_xticklabels() + ax.get_yticklabels() + ax.get_zticklabels():
            label.set_fontsize(9)
        
        # 3D图表需要特殊处理布局
        self.rul_figure.subplots_adjust(left=0.1, right=0.9, top=0.9, bottom=0.1)
        
    def create_trend_plots(self):
        """创建趋势图网格"""
        feature_names = [
            self.lang_manager.get_text('average_amplitude'),
            self.lang_manager.get_text('standard_deviation'),
            self.lang_manager.get_text('rms_amplitude'), 
            self.lang_manager.get_text('rms_absolute_amplitude'),
            self.lang_manager.get_text('kurtosis_coefficient'),
            self.lang_manager.get_text('skewness_coefficient')
        ]
        
        for i in range(6):
            ax = self.trend_figure.add_subplot(2, 3, i+1)
            
            # 生成示例数据
            x = np.arange(1, 28)
            y = np.random.normal(5, 1, 27) + np.linspace(0, 2, 27)
            
            ax.scatter(x, y, c='blue', s=30)
            ax.set_xlabel(self.lang_manager.get_text('time'), fontsize=9)
            ax.set_ylabel(feature_names[i], fontsize=9)
            ax.grid(True, alpha=0.3)
            
            # 设置字体
            for label in ax.get_xticklabels() + ax.get_yticklabels():
                label.set_fontsize(8)
            
        self.trend_figure.tight_layout()
        
    def on_nav_button_clicked(self, index):
        """导航按钮点击事件"""
        # 重置所有按钮样式
        for btn in self.nav_buttons:
            btn.setStyleSheet("""
                QPushButton {
                    background-color: transparent;
                    color: white;
                    border: 2px solid transparent;
                    border-radius: 5px;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: rgba(255, 255, 255, 0.2);
                }
                QPushButton:pressed {
                    background-color: rgba(255, 255, 255, 0.3);
                }
            """)
            
        # 高亮当前选中的按钮
        self.nav_buttons[index].setStyleSheet("""
            QPushButton {
                background-color: rgba(255, 255, 255, 0.3);
                color: white;
                border: 2px solid rgba(255, 255, 255, 0.5);
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        
        # 显示对应页面
        self.show_page(index)
        
    def show_page(self, index):
        """显示指定页面"""
        pages = [
            self.data_analysis_widget,
            self.feature_extraction_widget,
            self.health_diagnosis_widget,
            self.realtime_monitoring_widget,
            self.history_query_widget,
            self.machine_structure_widget
        ]
        
        # 隐藏所有页面
        for page in pages:
            page.hide()
            
        # 显示选中的页面
        pages[index].show()
        
    def on_language_changed(self, language_text):
        """语言切换处理函数"""
        if language_text == '简体中文':
            self.lang_manager.switch_language('zh_CN')
        elif language_text == 'English':
            self.lang_manager.switch_language('en_US')
            
        # 更新界面文本
        self.update_ui_text()
        
    def update_ui_text(self):
        """更新界面文本"""
        # 更新头部文本
        self.title_label.setText(self.lang_manager.get_text('title'))
        self.subtitle_label.setText(self.lang_manager.get_text('subtitle'))
        
        # 更新导航按钮文本
        nav_texts = [
            'nav_data_analysis', 'nav_feature_extraction', 'nav_health_diagnosis', 
            'nav_realtime_monitoring', 'nav_history_query', 'nav_machine_structure'
        ]
        for i, text_key in enumerate(nav_texts):
            self.nav_buttons[i].setText(self.lang_manager.get_text(text_key))
            
        # 更新各个页面的文本
        self.update_data_analysis_text()
        self.update_feature_extraction_text()
        self.update_health_diagnosis_text()
        self.update_realtime_monitoring_text()
        self.update_history_query_text()
        self.update_machine_structure_text()
        
    def update_data_analysis_text(self):
        """更新数据分析页面文本"""
        if hasattr(self, 'data_analysis_widget'):
            # 更新按钮文本
            button_texts = [
                'open_data_template', 'import_data', 'data_preprocessing',
                'raw_data_visualization', 'cleaned_visualization'
            ]
            for i, btn in enumerate(self.data_analysis_buttons):
                if i < len(button_texts):
                    btn.setText(self.lang_manager.get_text(button_texts[i]))
                    
            # 更新表格标题
            if hasattr(self, 'raw_data_table'):
                headers = [
                    self.lang_manager.get_text('measurement_time'),
                    self.lang_manager.get_text('pressure'),
                    self.lang_manager.get_text('vibration'),
                    self.lang_manager.get_text('stress'),
                    self.lang_manager.get_text('temperature')
                ]
                self.raw_data_table.setHorizontalHeaderLabels(headers)
                
            if hasattr(self, 'processed_data_table'):
                headers = [
                    self.lang_manager.get_text('measurement_time'),
                    self.lang_manager.get_text('pressure'),
                    self.lang_manager.get_text('vibration'),
                    self.lang_manager.get_text('stress'),
                    self.lang_manager.get_text('temperature')
                ]
                self.processed_data_table.setHorizontalHeaderLabels(headers)
                
            # 重新生成图表
            if hasattr(self, 'raw_figure'):
                self.raw_figure.clear()
                self.create_pressure_plot(self.raw_figure, self.lang_manager.get_text('raw_data_pressure_chart'), 0, 200)
                self.raw_canvas.draw()
                
            if hasattr(self, 'cleaned_figure'):
                self.cleaned_figure.clear()
                self.create_pressure_plot(self.cleaned_figure, self.lang_manager.get_text('cleaned_pressure_chart'), -1.5, 1.0)
                self.cleaned_canvas.draw()
                
    def update_feature_extraction_text(self):
        """更新特征提取页面文本"""
        if hasattr(self, 'feature_extraction_widget'):
            # 更新按钮文本
            buttons = self.feature_extraction_widget.findChildren(QPushButton)
            button_texts = [
                'open_data_template', 'import_data', 'key_feature_extraction', 'feature_selection'
            ]
            for i, btn in enumerate(buttons):
                if i < len(button_texts):
                    btn.setText(self.lang_manager.get_text(button_texts[i]))
                    
            # 更新表格标题
            if hasattr(self, 'feature_data_table'):
                headers = [
                    self.lang_manager.get_text('measurement_time'),
                    self.lang_manager.get_text('average_amplitude'),
                    self.lang_manager.get_text('standard_deviation'),
                    self.lang_manager.get_text('rms_amplitude'),
                    self.lang_manager.get_text('rms_absolute_amplitude'),
                    self.lang_manager.get_text('kurtosis_coefficient')
                ]
                self.feature_data_table.setHorizontalHeaderLabels(headers)
                
            # 重新生成图表
            if hasattr(self, 'feature_figure'):
                self.feature_figure.clear()
                self.create_feature_scatter_plot()
                self.feature_canvas.draw()
                
    def update_health_diagnosis_text(self):
        """更新健康诊断页面文本"""
        if hasattr(self, 'health_diagnosis_widget'):
            # 更新算法选择下拉框
            if hasattr(self, 'algo_combo'):
                self.algo_combo.clear()
                self.algo_combo.addItems([
                    self.lang_manager.get_text('dropdown_select_algorithm'),
                    self.lang_manager.get_text('algorithm_1'),
                    self.lang_manager.get_text('algorithm_2'),
                    self.lang_manager.get_text('algorithm_3')
                ])
                
            if hasattr(self, 'rul_algo_combo'):
                self.rul_algo_combo.clear()
                self.rul_algo_combo.addItems([
                    self.lang_manager.get_text('dropdown_select_algorithm'),
                    self.lang_manager.get_text('algorithm_1'),
                    self.lang_manager.get_text('algorithm_2'),
                    self.lang_manager.get_text('algorithm_3')
                ])
                
            # 重新生成图表
            if hasattr(self, 'health_figure'):
                self.health_figure.clear()
                self.create_health_index_plot()
                self.health_canvas.draw()
                
            if hasattr(self, 'rul_figure'):
                self.rul_figure.clear()
                self.create_rul_prediction_plot()
                self.rul_canvas.draw()
                
    def update_realtime_monitoring_text(self):
        """更新实时监控页面文本"""
        if hasattr(self, 'health_status_table'):
            # 更新表格标题
            headers = [
                self.lang_manager.get_text('normal'),
                self.lang_manager.get_text('abnormal'),
                self.lang_manager.get_text('fault')
            ]
            self.health_status_table.setHorizontalHeaderLabels(headers)
            
            row_labels = [
                self.lang_manager.get_text('die_casting_sleeve'),
                self.lang_manager.get_text('tie_bar'),
                self.lang_manager.get_text('fixed_platen'),
                self.lang_manager.get_text('moving_platen')
            ]
            self.health_status_table.setVerticalHeaderLabels(row_labels)
            
            # 重新生成图表
            if hasattr(self, 'trend_figure'):
                self.trend_figure.clear()
                self.create_trend_plots()
                self.trend_canvas.draw()
                
    def update_history_query_text(self):
        """更新历史查询页面文本"""
        if hasattr(self, 'history_query_widget'):
            # 直接更新保存的标签引用
            if hasattr(self, 'time_label'):
                self.time_label.setText(self.lang_manager.get_text('time_range_label'))
                
            if hasattr(self, 'data_type_label'):
                self.data_type_label.setText(self.lang_manager.get_text('data_type_label'))
                
            if hasattr(self, 'export_type_label'):
                self.export_type_label.setText(self.lang_manager.get_text('report_export_type_label'))
                    
            # 更新输入框默认值
            if hasattr(self, 'time_input'):
                self.time_input.setText(self.lang_manager.get_text('time_range_default'))
                
            if hasattr(self, 'data_type_input'):
                self.data_type_input.setText(self.lang_manager.get_text('data_type_default'))
                    
            # 更新表格标题
            if hasattr(self, 'history_table'):
                headers = [
                    self.lang_manager.get_text('measurement_time'),
                    self.lang_manager.get_text('die_casting_sleeve'),
                    self.lang_manager.get_text('tie_bar'),
                    self.lang_manager.get_text('fixed_platen'),
                    self.lang_manager.get_text('moving_platen'),
                    self.lang_manager.get_text('remarks')
                ]
                self.history_table.setHorizontalHeaderLabels(headers)
                
                # 更新表格数据中的状态文本
                for i in range(18):
                    for j in range(1, 5):
                        item = self.history_table.item(i, j)
                        if item and item.text() in ['正常', 'Normal']:
                            item.setText(self.lang_manager.get_text('normal_status'))
                        elif item and item.text() in ['异常', 'Abnormal']:
                            item.setText(self.lang_manager.get_text('abnormal_status'))
                        elif item and item.text() in ['故障', 'Fault']:
                            item.setText(self.lang_manager.get_text('fault_status'))
                            
                    # 更新备注列
                    remarks_item = self.history_table.item(i, 5)
                    if remarks_item:
                        if remarks_item.text() in ['哥林柱传感器数据异常', 'Tie bar sensor data abnormal']:
                            remarks_item.setText(self.lang_manager.get_text('tie_bar_sensor_error'))
                        elif remarks_item.text() in ['哥林柱断裂', 'Tie bar fractured']:
                            remarks_item.setText(self.lang_manager.get_text('tie_bar_break'))
                
            # 更新导出下拉框
            if hasattr(self, 'export_type_combo'):
                self.export_type_combo.clear()
                self.export_type_combo.addItems([
                    self.lang_manager.get_text('pdf_export'),
                    self.lang_manager.get_text('excel_export'),
                    self.lang_manager.get_text('word_export')
                ])
                self.export_type_combo.setCurrentText(self.lang_manager.get_text('pdf_export'))
                    
            # 更新导出按钮
            if hasattr(self, 'export_btn'):
                self.export_btn.setText(self.lang_manager.get_text('export_button'))
                    
    def update_machine_structure_text(self):
        """更新机器结构图页面文本"""
        # 机器结构图页面不需要文本更新，因为移除了标题
        pass

    def import_data_file(self):
        """导入数据文件"""
        try:
            # 打开文件对话框
            file_dialog = QFileDialog()
            file_path, _ = file_dialog.getOpenFileName(
                self,
                self.lang_manager.get_text('select_data_file'),
                "",
                f"{self.lang_manager.get_text('data_files')};;{self.lang_manager.get_text('csv_files')};;{self.lang_manager.get_text('excel_files')};;{self.lang_manager.get_text('all_files')}"
            )
            
            if file_path:
                # 根据文件扩展名读取数据
                if file_path.lower().endswith('.csv'):
                    df = pd.read_csv(file_path)
                elif file_path.lower().endswith(('.xlsx', '.xls')):
                    df = pd.read_excel(file_path)
                else:
                    QMessageBox.warning(self, self.lang_manager.get_text('error'), self.lang_manager.get_text('unsupported_format'))
                    return
                
                # 检查数据列数
                if len(df.columns) < 5:
                    QMessageBox.warning(self, self.lang_manager.get_text('error'), self.lang_manager.get_text('insufficient_columns'))
                    return
                
                # 更新原始数据表格
                self.update_raw_data_table(df)
                
                # 显示成功消息
                QMessageBox.information(self, self.lang_manager.get_text('success'), f"{self.lang_manager.get_text('import_success')}{file_path}")
                
        except Exception as e:
            QMessageBox.critical(self, self.lang_manager.get_text('error'), f"{self.lang_manager.get_text('import_error')}{str(e)}")
    
    def update_raw_data_table(self, df):
        """更新原始数据表格"""
        # 获取前8行数据（或所有数据如果少于8行）
        display_data = df.head(8)
        
        # 设置表格行数
        self.raw_data_table.setRowCount(len(display_data))
        
        # 填充数据
        for i, (index, row) in enumerate(display_data.iterrows()):
            for j, value in enumerate(row):
                item = QTableWidgetItem(str(value))
                self.raw_data_table.setItem(i, j, item)
                # 压力列高亮（第二列）
                if j == 1:
                    item.setBackground(QColor(255, 255, 0, 100))
        
        # 更新表格标题（使用数据文件的列名）
        headers = list(df.columns[:5])  # 取前5列
        self.raw_data_table.setHorizontalHeaderLabels(headers)
        
        # 保存完整数据用于后续处理
        self.current_data = df

def main():
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    # 创建主窗口
    window = HealthMonitoringSystem()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main() 